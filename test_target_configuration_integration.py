#!/usr/bin/env python3
"""
Test script for the enhanced target configuration functionality.
Tests both backend API endpoints and integration with dataset_samples table.
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_list_datasets():
    """Test the datasets listing endpoint."""
    print("🧪 Testing datasets listing endpoint...")
    
    try:
        response = requests.get(f"{BASE_URL}/datasets")
        response.raise_for_status()
        data = response.json()
        
        print(f"✅ Success! Found {data['total_count']} datasets")
        for dataset in data['datasets'][:3]:  # Show first 3
            print(f"   - {dataset['name']} ({dataset['total_samples']} samples)")
        
        return data['datasets']
    except Exception as e:
        print(f"❌ Failed: {e}")
        return []

def test_remote_model_with_dataset(datasets):
    """Test the remote model testing endpoint with dataset selection."""
    if not datasets:
        print("⚠️ No datasets available for testing")
        return False
    
    print(f"\n🧪 Testing remote model with dataset: {datasets[0]['name']}")
    
    # Test request with dataset selection
    test_request = {
        "target_llm_config": {
            "model": "gpt-3.5-turbo",
            "api_key": "test-key",  # This will use fallback from base.yml
            "base_url": "https://api.openai.com/v1",
            "provider": "openai",
            "temperature": 0.7,
            "max_tokens": 512,
            "top_p": 0.9
        },
        "dataset_id": datasets[0]['id'],  # Use first available dataset
        "num_samples": 2,
        "num_mutations": 1,
        "max_iters": 1
    }
    
    try:
        response = requests.post(f"{BASE_URL}/test-remote-model", json=test_request)
        response.raise_for_status()
        result = response.json()
        
        print(f"✅ Test completed with status: {result['status']}")
        if result['status'] == 'completed':
            print(f"📊 Results: {result['total_responses_generated']} responses, avg score: {result['average_score']}")
            if result.get('results'):
                print(f"📋 Sample result: {result['results'][0]['adv_prompt'][:50]}...")
        else:
            print(f"❌ Test failed: {result.get('error_details', 'Unknown error')}")
        
        return True
    except Exception as e:
        print(f"❌ Failed: {e}")
        return False

def test_remote_model_with_custom_prompts():
    """Test the remote model testing endpoint with custom prompts."""
    print("\n🧪 Testing remote model with custom prompts...")
    
    custom_prompts = [
        "What is the capital of France?",
        "Explain quantum computing in simple terms.",
        "Write a short poem about technology."
    ]
    
    # Test request with custom prompts
    test_request = {
        "target_llm_config": {
            "model": "gpt-3.5-turbo",
            "api_key": "test-key",  # This will use fallback from base.yml
            "base_url": "https://api.openai.com/v1",
            "provider": "openai",
            "temperature": 0.7,
            "max_tokens": 512,
            "top_p": 0.9
        },
        "custom_prompts": custom_prompts,
        "num_samples": 2,
        "num_mutations": 1,
        "max_iters": 1
    }
    
    try:
        response = requests.post(f"{BASE_URL}/test-remote-model", json=test_request)
        response.raise_for_status()
        result = response.json()
        
        print(f"✅ Test completed with status: {result['status']}")
        if result['status'] == 'completed':
            print(f"📊 Results: {result['total_responses_generated']} responses, avg score: {result['average_score']}")
        else:
            print(f"❌ Test failed: {result.get('error_details', 'Unknown error')}")
        
        return True
    except Exception as e:
        print(f"❌ Failed: {e}")
        return False

def test_dataset_samples_loading(datasets):
    """Test loading samples from dataset_samples table."""
    if not datasets:
        print("⚠️ No datasets available for testing")
        return False
    
    print(f"\n🧪 Testing dataset samples loading for: {datasets[0]['name']}")
    
    try:
        dataset_id = datasets[0]['id']
        response = requests.get(f"{BASE_URL}/datasets/samples?generated_dataset_id={dataset_id}&limit=5")
        response.raise_for_status()
        data = response.json()
        
        print(f"✅ Success! Found {data['total_count']} samples")
        for sample in data['samples'][:2]:  # Show first 2
            sample_content = sample.get('sample_content', {})
            prompt = (sample_content.get('prompt') or 
                     sample_content.get('question') or 
                     sample_content.get('input') or 
                     sample_content.get('text') or 
                     str(sample_content)[:50])
            print(f"   - Sample: {prompt}...")
        
        return True
    except Exception as e:
        print(f"❌ Failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Testing Enhanced Target Configuration")
    print("=" * 50)
    
    # Test 1: List datasets
    datasets = test_list_datasets()
    
    # Test 2: Test remote model with dataset
    if datasets:
        test_remote_model_with_dataset(datasets)
    
    # Test 3: Test remote model with custom prompts
    test_remote_model_with_custom_prompts()
    
    # Test 4: Test dataset samples loading
    if datasets:
        test_dataset_samples_loading(datasets)
    
    print("\n🎉 All tests completed!")
    print("\n📋 Next Steps:")
    print("1. Start the backend server: uvicorn rainbowplus.api.app:app --host 0.0.0.0 --port 8000")
    print("2. Start the client: cd client && npm run dev")
    print("3. Navigate to the 'Automate Testing' section")
    print("4. Test the enhanced Target Configuration with dataset selection")

if __name__ == "__main__":
    main()
