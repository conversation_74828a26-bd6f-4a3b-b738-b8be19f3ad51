FROM python:3.10
WORKDIR /code

# Copy requirements and pre-downloaded packages
COPY ./requirements.txt /code/requirements.txt
COPY ./pip_packages /code/pip_packages

# Install packages - first try local, then fall back to mirror
RUN pip install --no-index --find-links=/code/pip_packages -r /code/requirements.txt || \
    pip install -r /code/requirements.txt --index-url https://mirrors.aliyun.com/pypi/simple/

# Copy application files
COPY ./configs /code/configs
COPY ./models /code/models
COPY ./data /code/da
COPY ./rainbowplus /code/rainbowplus

CMD ["python", "-m", "rainbowplus.api.app"]