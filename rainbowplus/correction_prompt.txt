When working on RainbowPlus project:
1. Use rbp environment (source rbp/bin/activate)
2. PostgreSQL (5432) and Redis (6379) are already running
3. Check .env.local for configuration
4. Don't modify Streamlit (user doesn't use it anymore). Check the client folder.
5. Use constructor-based dependency injection for new backend features
6. Use package managers, not manual file editing
7. When token limits fail, check original base.yml settings first
8. Database schema changes require server restart
9. Always investigate why existing code works before changing it
10. Default project "Default Project" exists for dataset generation without specified project

DATASET GENERATION DEBUGGING GUIDE:
11. Add comprehensive logging to dataset generation pipeline:
    - Log when dataset generation request is received
    - Log configuration creation and validation
    - Log LLM initialization and model details
    - Log prompt formatting and content
    - Log API calls to OpenAI with request/response details
    - Log sample generation progress and results
    - Log database save operations
    - Log any errors with full stack traces

12. Key logging points for dataset generation:
    - DataPreparationService.generate_dataset(): Log request parameters
    - DataPreparationService._execute_data_preparation_pipeline(): Log pipeline start/progress
    - Pipeline.generate(): Log LLM calls and sample generation
    - LLMviaOpenAI.generate_format(): Log API requests and responses
    - Database operations: Log job status updates and sample saves

13. Check these logs when dataset generation seems stuck:
    - Server logs for API request processing
    - Pipeline execution logs for LLM calls
    - Database logs for job status and sample creation
    - OpenAI API response logs for generation results

14. Common dataset generation issues:
    - API key authentication failures
    - Token limit exceeded errors
    - Network timeouts to OpenAI API
    - Database connection issues
    - Invalid configuration parameters