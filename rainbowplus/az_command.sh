az containerapp create \
  --name demoda \
  --resource-group rg-groktesting \
  --environment da \
  --image deepassure.azurecr.io/rainbowplus:latest \
  --env-vars REDIS_URL=redis://redis:6379/0 THIRD_PARTY_API_KEY=aaa \
  --target-port 8000 \
  --ingress external \
  --registry-server deepassure.azurecr.io \
  --registry-username deepassure \
  --registry-password upY3/FyducxbbS5BAAFy1ePirG7Iii/JIibxhJlwUr+ACRD/JajM 

  az containerapp logs show \
  --name demoda \
  --resource-group rg-groktesting \
  --revision demoda--d2w2zse \
  --follow

  az containerapp delete \
  --name demoda \
  --resource-group rg-groktesting

  az containerapp create \
  --name postgres-app \
  --resource-group rg-groktesting \
  --environment da \
  --image postgres:latest \
  --env-vars "POSTGRES_USER=admin" "POSTGRES_PASSWORD=yourpassword" "POSTGRES_DB=mydb" \
  --cpu 0.5 \
  --memory 1Gi \
  --target-port 5432 \
  --ingress external

  az containerapp create \
  --name redis-app \
  --resource-group rg-groktesting \
  --environment da \
  --image redis:latest \
  --cpu 0.5 \
  --memory 1Gi \
  --target-port 6379 \
  --ingress external

  az containerapp create \
  --name demoda \
  --resource-group rg-groktesting \
  --environment da \
  --image deepassure.azurecr.io/rainbowplus:latest \
  --env-vars REDIS_PORT=6379 REDIS_HOST=************** \
  --target-port 8000 \
  --ingress external \
  --registry-server deepassure.azurecr.io \
  --registry-username deepassure \
  --registry-password upY3/FyducxbbS5BAAFy1ePirG7Iii/JIibxhJlwUr+ACRD/JajM 
  
REDIS_PORT=6379
==========
az containerapp delete \\n  --name demoda \\n  --resource-group rg-groktesting
az containerapp env create --name da --resource-group deepassure-rg --location southeastasia
az containerapp env delete -n da -g deepassure-rg -y
az containerapp create \
--name postgres-app \
--resource-group deepassure-rg \
--environment da \
--image postgres:latest \
--env-vars POSTGRES_USER=admin POSTGRES_PASSWORD=yourpassword POSTGRES_DB=mydb POSTGRES_PORT=5432 SLACK_WEBHOOK_URL=*********************************************************************************\
--cpu 0.5 \
--memory 1Gi \
--target-port 5432 \
--ingress external

az containerapp create \
--name redis-app \
--resource-group deepassure-rg \
--environment da \
--image redis:latest \
--cpu 0.5 \
--memory 1Gi \
--target-port 6379 \
--ingress external

sudo docker tag rainbowplus deepassuresg.azurecr.io/rainbowplus:latest
sudo docker push deepassuresg.azurecr.io/rainbowplus:latest

az containerapp create \
--name demoda \
--resource-group deepassure-rg \
--environment da \
--image deepassuresg.azurecr.io/rainbowplus:latest \
--env-vars POSTGRES_HOST=deepassuredb.postgres.database.azure.com POSTGRES_USER=da POSTGRES_PASSWORD=deepassure2025@ POSTGRES_DB=mydb POSTGRES_PORT=5432 POSTGRES_SSL=TRUE REDIS_PORT=6380 REDIS_HOST=deepassuresg.redis.cache.windows.net REDIS_KEY=0A7RllqmDlVKuFO1w0jjfUMdeEUgAjHYIAzCaIuEPvI= REDIS_TLS=TRUE SLACK_WEBHOOK_URL=********************************************************************************* \
--target-port 8000 \
--ingress external \
--registry-server deepassuresg.azurecr.io \
--registry-username deepassuresg \
--registry-password **************************************************** 

sudo docker tag rainbowplus-queue deepassuresg.azurecr.io/rainbowplus-queue:latest
sudo docker push deepassuresg.azurecr.io/rainbowplus-queue:latest

az containerapp create \
--name daqueue \
--resource-group deepassure-rg \
--environment da \
--image deepassuresg.azurecr.io/rainbowplus-queue:latest \
--env-vars POSTGRES_HOST=deepassuredb.postgres.database.azure.com POSTGRES_USER=da POSTGRES_PASSWORD=deepassure2025@ POSTGRES_DB=mydb POSTGRES_PORT=5432 POSTGRES_SSL=TRUE REDIS_PORT=6380 REDIS_HOST=deepassuresg.redis.cache.windows.net REDIS_KEY=0A7RllqmDlVKuFO1w0jjfUMdeEUgAjHYIAzCaIuEPvI= REDIS_TLS=TRUE SLACK_WEBHOOK_URL=********************************************************************************* \
--target-port 8000 \
--ingress external \
--registry-server deepassuresg.azurecr.io \
--registry-username deepassuresg \
--registry-password **************************************************** 


pip install --no-index --find-links=./pip_packages -r ./requirements.txt

az containerapp show \
--name demoda \
--resource-group deepassure-rg \
--query configuration.ingress.fqdn \
--output tsv

az containerapp show \
--name demoda \
--resource-group deepassure-rg

az containerapp ingress show \
  --name demoda \
  --resource-group deepassure-rg

  az containerapp update \
  --name daqueue \
  --resource-group deepassure-rg \
  --image deepassuresg.azurecr.io/rainbowplus-queue:latest  

  az containerapp update \
  --name daqueue \
  --resource-group deepassure-rg \
  --probe-liveness "type=exec,command='python /code/celery_health_monitor.py --check-workers',initialDelay=90,period=10" \
  --probe-readiness "type=exec,command='python /code/celery_health_monitor.py --check-workers',initialDelay=30,period=10"

export POSTGRES_USER=da
export POSTGRES_PASSWORD=deepassure2025@
export POSTGRES_DB=mydb
export POSTGRES_SSL=true
export POSTGRES_HOST=deepassuredb.postgres.database.azure.com
export POSTGRES_PORT=5432
export REDIS_HOST=deepassuresg.redis.cache.windows.net
export REDIS_PORT=6380
export REDIS_KEY=0A7RllqmDlVKuFO1w0jjfUMdeEUgAjHYIAzCaIuEPvI=
export REDIS_TLS=true
export SLACK_WEBHOOK_URL=*********************************************************************************
#
refactor the selected code

divide the function into smaller function

1st: take the same input and just generate mutated prompts, return the prompts
2nd: take the promp list 

check def run_rainbowplus(
    args, config, seed_prompts=[], llms=None, fitness_fn=None, similarity_fn=None, custom_prompts=None
)
i want to save all adv_prompts, responses, scores, to database for later use. and job must have link to adv prompt, prompt link to response, and score to response
Trace from @/home/<USER>/ai/knsoft/rainbowplus/rainbowplus/api/app.py  , to see where the flow started, and what is recorded to the database each run

gitlab token **************************

qdrant eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2Nlc3MiOiJtIn0.KDYPPJUREhGTZROnbnCIHSTzQ7KpD_0PyEO5phAdrds