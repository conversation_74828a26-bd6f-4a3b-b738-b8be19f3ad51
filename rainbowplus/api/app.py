import uvicorn
from fastapi import <PERSON>AP<PERSON>, HTTPException
from fastapi.responses import JSONResponse
from .models import RainbowPlusRequest, ProjectCreateRequest, ProjectResponse, ProjectListResponse, RecentProjectsResponse, ProjectDashboardListResponse, DatasetSampleResponse, DatasetSamplesListResponse, DatagenEventListResponse, DatagenEventDetailResponse, AlertResponse, AlertsListResponse, TestRemoteModelRequest, TestRemoteModelResponse, DatasetListResponse
from .services import RainbowPlusService, CheckpointedAsyncStrategy, ProjectService
from .health import health_checker
import logging

logger = logging.getLogger(__name__)

# Create the FastAPI app
app = FastAPI(
    title="RainbowPlus API",
    description="Adversarial prompt generation and analysis API",
    version="1.0.0"
)

# Health Check Endpoints

@app.get("/health")
async def health_check():
    """
    Basic health check endpoint for Azure and other cloud platforms.
    Returns 200 OK if the service is running.
    """
    try:
        app_info = await health_checker.get_application_info()
        return {
            "status": "healthy",
            "timestamp": app_info["start_time"],
            "service": "RainbowPlus API",
            "version": app_info["version"]
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail="Service unhealthy")


# @app.get("/health/detailed")
# async def detailed_health_check():
#     """
#     Detailed health check including all dependencies.
#     Use this for comprehensive monitoring.
#     """
#     try:
#         # Run all health checks
#         database_health = await health_checker.check_database()
#         redis_health = await health_checker.check_redis()
#         celery_health = await health_checker.check_celery()
#         system_health = await health_checker.check_system_resources()
#         app_info = await health_checker.get_application_info()

#         # Determine overall status
#         all_checks = [database_health, redis_health, celery_health, system_health]
#         unhealthy_checks = [check for check in all_checks if check.get("status") != "healthy"]

#         overall_status = "healthy"
#         if unhealthy_checks:
#             # If database (critical service) is down, mark as unhealthy
#             critical_unhealthy = database_health.get("status") == "unhealthy"
#             overall_status = "unhealthy" if critical_unhealthy else "degraded"

#         response = {
#             "status": overall_status,
#             "timestamp": app_info["start_time"],
#             "checks": {
#                 "application": app_info,
#                 "database": database_health,
#                 "redis": redis_health,
#                 "celery": celery_health,
#                 "system": system_health
#             }
#         }

#         # Return appropriate HTTP status
#         if overall_status == "unhealthy":
#             return JSONResponse(status_code=503, content=response)
#         elif overall_status == "degraded":
#             return JSONResponse(status_code=200, content=response)
#         else:
#             return response

#     except Exception as e:
#         logger.error(f"Detailed health check failed: {e}")
#         raise HTTPException(status_code=503, detail=f"Health check error: {str(e)}")


# @app.get("/health/database")
# async def database_health_check():
#     """Database-specific health check."""
#     try:
#         result = await health_checker.check_database()
#         if result.get("status") == "healthy":
#             return result
#         else:
#             return JSONResponse(status_code=503, content=result)
#     except Exception as e:
#         logger.error(f"Database health check failed: {e}")
#         raise HTTPException(status_code=503, detail=f"Database health check error: {str(e)}")


# @app.get("/health/celery")
# async def celery_health_check():
#     """Celery worker health check."""
#     try:
#         result = await health_checker.check_celery()
#         if result.get("status") in ["healthy", "degraded"]:
#             return result
#         else:
#             return JSONResponse(status_code=503, content=result)
#     except Exception as e:
#         logger.error(f"Celery health check failed: {e}")
#         raise HTTPException(status_code=503, detail=f"Celery health check error: {str(e)}")


# @app.get("/health/redis")
# async def redis_health_check():
#     """Redis health check."""
#     try:
#         result = await health_checker.check_redis()
#         if result.get("status") == "healthy":
#             return result
#         else:
#             return JSONResponse(status_code=503, content=result)
#     except Exception as e:
#         logger.error(f"Redis health check failed: {e}")
#         raise HTTPException(status_code=503, detail=f"Redis health check error: {str(e)}")


# Main API Endpoint

@app.get("/datasets", response_model=DatasetListResponse)
async def list_datasets(limit: int = 50, offset: int = 0):
    """
    List available datasets for target configuration testing.

    Args:
        limit: Maximum number of datasets to return (default: 50, max: 100)
        offset: Number of datasets to skip (default: 0)

    Returns:
        DatasetListResponse: List of available datasets
    """
    try:
        # Validate limit parameter
        if limit < 1 or limit > 100:
            raise HTTPException(status_code=400, detail="Limit must be between 1 and 100")

        if offset < 0:
            raise HTTPException(status_code=400, detail="Offset must be non-negative")

        result = dataset_list_service.list_available_datasets(limit=limit, offset=offset)
        logger.debug(f"Listed {len(result.datasets)} datasets")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error listing datasets: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/test-remote-model", response_model=TestRemoteModelResponse)
async def test_remote_model(request: TestRemoteModelRequest):
    """
    Test a remote model with custom prompts for target configuration.

    This endpoint allows testing a target LLM with custom prompts loaded from
    dataset_samples table or provided directly to evaluate its behavior before
    running full adversarial analysis.
    """
    try:
        logger.info(f"🧪 Received remote model test request for model: {request.target_llm_config.model}")
        result = remote_model_test_service.test_remote_model(request)
        logger.info(f"✅ Remote model test completed with status: {result.status}")
        return result
    except Exception as e:
        logger.error(f"❌ Error in remote model test: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Remote model test failed: {str(e)}")


@app.post("/rainbowplus")
async def run_rainbowplus_api(request: RainbowPlusRequest):
    """
    Run RainbowPlus adversarial prompt analysis.
    """
    strategy = CheckpointedAsyncStrategy()  # Create an instance of CheckpointedAsyncStrategy
    return strategy.run_analysis(
        prompts=request.prompts,
        target_llm=request.target_llm,
        num_samples=request.num_samples,
        num_mutations=request.num_mutations,
        max_iters=request.max_iters,
        api_key=request.api_key,
        base_url=request.base_url,
        nickname=request.nickname,
        project_id=request.project_id
    )


# Project Management Endpoints

# Create service instances using constructor-based dependency injection
from .models import DatasetGenerationRequest, DatasetGenerationResponse, DatagenEventRequest, DatagenEventResponse
from .services import DataPreparationService, DatagenEventService, DatasetListService, RemoteModelTestService

project_service = ProjectService()
data_preparation_service = DataPreparationService()
datagen_event_service = DatagenEventService()
dataset_list_service = DatasetListService()
remote_model_test_service = RemoteModelTestService()

@app.post("/datasets/generate", response_model=DatasetGenerationResponse)
async def generate_dataset(request: DatasetGenerationRequest):
    """
    API endpoint to receive dataset generation form data and trigger data preparation pipeline.

    This endpoint integrates with the data preparation workflow using dynamic configuration
    instead of static YAML files.
    """
    try:
        # Use the new data preparation service
        response = data_preparation_service.generate_dataset(request)
        return response
    except Exception as e:
        logger.error(f"Error generating dataset: {e}")
        raise HTTPException(status_code=500, detail="Failed to start dataset generation")


@app.post("/datagen/events", response_model=DatagenEventResponse)
async def create_datagen_event(request: DatagenEventRequest):
    """
    API endpoint to capture dataset generation form data when transitioning from step 3 to step 4.

    This endpoint is called when the user clicks "Next" after completing the Expert Collaboration
    step (step 3) in the dataset generation workflow.
    """
    try:
        response = datagen_event_service.create_datagen_event(request)
        return response
    except Exception as e:
        logger.error(f"Error creating datagen event: {e}")
        raise HTTPException(status_code=500, detail="Failed to record dataset generation event")


@app.get("/datagen/events/{event_id}", response_model=DatagenEventResponse)
async def get_datagen_event(event_id: str):
    """
    Get a dataset generation event by ID.
    """
    try:
        response = datagen_event_service.get_datagen_event(event_id)
        if not response:
            raise HTTPException(status_code=404, detail="Dataset generation event not found")
        return response
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving datagen event: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve dataset generation event")


@app.get("/datagen/events", response_model=DatagenEventListResponse)
async def list_datagen_events(
    expert_id: str = None,
    project_id: str = None,
    domain: str = None,
    limit: int = 100,
    offset: int = 0
):
    """
    List dataset generation events with optional filtering for expert review.

    Args:
        expert_id: Filter by expert ID (events where this expert is assigned)
        project_id: Filter by project ID
        domain: Filter by application domain
        limit: Maximum number of events to return (default: 100, max: 1000)
        offset: Number of events to skip (default: 0)
    """
    try:
        # Validate limit parameter
        if limit < 1 or limit > 1000:
            raise HTTPException(status_code=400, detail="Limit must be between 1 and 1000")

        if offset < 0:
            raise HTTPException(status_code=400, detail="Offset must be non-negative")

        result = datagen_event_service.list_datagen_events(
            expert_id=expert_id,
            project_id=project_id,
            domain=domain,
            limit=limit,
            offset=offset
        )
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error listing datagen events: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@app.post("/projects", response_model=ProjectResponse)
async def create_project(request: ProjectCreateRequest):
    """
    Create a new project.
    """
    try:
        return project_service.create_project(request)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating project: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/projects", response_model=ProjectDashboardListResponse)
async def list_projects():
    """
    List all projects.
    """
    try:
        result = project_service.list_projects()
        logger.debug(f"List projects result: {result}")
        # result is already a ProjectDashboardListResponse, so just return it
        return result
    except Exception as e:
        logger.error(f"Error listing projects: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/projects/recent", response_model=RecentProjectsResponse)
async def get_recent_projects(limit: int = 10):
    """
    Get recent projects with dashboard statistics.

    Args:
        limit: Maximum number of projects to return (default: 10, max: 50)
    """
    try:
        # Validate limit parameter
        if limit < 1 or limit > 50:
            raise HTTPException(status_code=400, detail="Limit must be between 1 and 50")

        result = project_service.get_recent_projects(limit)
        logger.debug(f"Recent projects result: {result}")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting recent projects: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/projects/{project_id}", response_model=ProjectResponse)
async def get_project(project_id: str):
    """
    Get a specific project by ID.
    """
    try:
        project = project_service.get_project(project_id)
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
        return project
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting project {project_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.put("/projects/{project_id}", response_model=ProjectResponse)
async def update_project(project_id: str, request: ProjectCreateRequest):
    """
    Update an existing project.
    """
    try:
        project = project_service.update_project(project_id, request)
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
        return project
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating project {project_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.delete("/projects/{project_id}")
async def delete_project(project_id: str):
    """
    Delete a project.
    """
    try:
        deleted = project_service.delete_project(project_id)
        if not deleted:
            raise HTTPException(status_code=404, detail="Project not found")
        return {"message": "Project deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting project {project_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


# Dataset Sample Endpoints

@app.get("/datasets/samples", response_model=DatasetSamplesListResponse)
async def list_dataset_samples(
    project_id: str = None,
    datagen_event_id: str = None,
    generated_dataset_id: str = None,
    label: str = None,
    limit: int = 100,
    offset: int = 0
):
    """
    List dataset samples with optional filtering.

    Args:
        project_id: Filter by project ID
        datagen_event_id: Filter by datagen event ID
        generated_dataset_id: Filter by generated dataset ID
        label: Filter by sample label
        limit: Maximum number of samples to return (default: 100, max: 1000)
        offset: Number of samples to skip (default: 0)
    """
    try:
        from .services import DatasetSampleService

        # Validate limit parameter
        if limit < 1 or limit > 1000:
            raise HTTPException(status_code=400, detail="Limit must be between 1 and 1000")

        if offset < 0:
            raise HTTPException(status_code=400, detail="Offset must be non-negative")

        dataset_sample_service = DatasetSampleService()
        result = dataset_sample_service.list_dataset_samples(
            project_id=project_id,
            datagen_event_id=datagen_event_id,
            generated_dataset_id=generated_dataset_id,
            label=label,
            limit=limit,
            offset=offset
        )
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error listing dataset samples: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/datasets/samples/{sample_id}", response_model=DatasetSampleResponse)
async def get_dataset_sample(sample_id: str):
    """
    Get a specific dataset sample by ID.
    """
    try:
        from .services import DatasetSampleService

        dataset_sample_service = DatasetSampleService()
        sample = dataset_sample_service.get_dataset_sample(sample_id)
        if not sample:
            raise HTTPException(status_code=404, detail="Dataset sample not found")
        return sample
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting dataset sample {sample_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.get("/projects/{project_id}/samples", response_model=DatasetSamplesListResponse)
async def get_project_samples(
    project_id: str,
    label: str = None,
    limit: int = 100,
    offset: int = 0
):
    """
    Get all dataset samples for a specific project.

    Args:
        project_id: The project ID
        label: Filter by sample label
        limit: Maximum number of samples to return (default: 100, max: 1000)
        offset: Number of samples to skip (default: 0)
    """
    try:
        from .services import DatasetSampleService

        # Validate limit parameter
        if limit < 1 or limit > 1000:
            raise HTTPException(status_code=400, detail="Limit must be between 1 and 1000")

        if offset < 0:
            raise HTTPException(status_code=400, detail="Offset must be non-negative")

        dataset_sample_service = DatasetSampleService()
        result = dataset_sample_service.list_dataset_samples(
            project_id=project_id,
            label=label,
            limit=limit,
            offset=offset
        )
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting samples for project {project_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/alerts", response_model=AlertsListResponse)
async def get_recent_alerts(limit: int = 20):
    """
    Get recent system alerts including job failures, completions, and dataset generation events.

    Args:
        limit: Maximum number of alerts to return (default: 20)

    Returns:
        AlertsListResponse: List of recent alerts with metadata
    """
    try:
        from .services import AlertService

        alert_service = AlertService()
        alerts_data = alert_service.get_recent_alerts(limit=limit)

        # Convert to AlertResponse objects
        alerts = [
            AlertResponse(
                id=alert["id"],
                type=alert["type"],
                title=alert["title"],
                description=alert["description"],
                project=alert["project"],
                timestamp=alert["timestamp"],
                source=alert["source"],
                details=alert["details"]
            )
            for alert in alerts_data
        ]

        return AlertsListResponse(
            alerts=alerts,
            total_count=len(alerts)
        )

    except Exception as e:
        logger.error(f"Error fetching alerts: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
