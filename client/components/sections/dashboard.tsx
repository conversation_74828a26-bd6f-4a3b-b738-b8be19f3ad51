"use client"

import Link from "next/link"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { AlertTriangle, Info, Zap, Loader2 } from "lucide-react"
import { useState, useEffect } from "react"
import { projectsApi, alertsApi } from "@/lib/api-client"

// TypeScript interfaces for API responses
interface ProjectDashboard {
  id: string
  name: string
  description: string
  domain: string
  status: "active" | "completed" | "pending"
  datasetsGenerated: number
  testsRun: number
  riskScore: number
  progress: number
  created_at: string
  updated_at: string
}

interface Alert {
  id: string
  type: "critical" | "warning" | "info"
  title: string
  description: string
  project: string | null
  timestamp: string
  source: string
  details?: Record<string, any>
}

const metrics = [
  { label: "Total Projects", value: "15" },
  { label: "Active Testing", value: "8" },
  { label: "Average Risk Score", value: "2.1" },
  { label: "Tests This Week", value: "2,340" },
  { label: "Critical Alerts", value: "3" },
  { label: "Datasets Generated", value: "12,500" },
]

const getStatusVariant = (status: string) => {
  switch (status) {
    case "active":
      return "default"
    case "completed":
      return "secondary"
    case "pending":
      return "outline"
    default:
      return "outline"
  }
}

const getRiskColor = (score: number) => {
  if (score < 2) return "text-green-600"
  if (score < 3) return "text-yellow-600"
  return "text-red-600"
}

const getAlertIcon = (type: string) => {
  switch (type) {
    case "critical":
      return <AlertTriangle className="h-4 w-4 text-red-500" />
    case "warning":
      return <Zap className="h-4 w-4 text-yellow-500" />
    case "info":
      return <Info className="h-4 w-4 text-blue-500" />
    default:
      return <Info className="h-4 w-4" />
  }
}

export function Dashboard() {
  const [projects, setProjects] = useState<ProjectDashboard[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [alerts, setAlerts] = useState<Alert[]>([])
  const [alertsLoading, setAlertsLoading] = useState(true)
  const [alertsError, setAlertsError] = useState<string | null>(null)

  // Fetch recent projects on component mount
  useEffect(() => {
    const fetchRecentProjects = async () => {
      try {
        setIsLoading(true)
        setError(null)

        const response = await projectsApi.recent(6) // Get 6 recent projects for the dashboard

        if (response.error) {
          throw new Error(response.error)
        }

        if (response.data && response.data.projects) {
          setProjects(response.data.projects)
        } else {
          setProjects([])
        }
      } catch (err) {
        console.error('Error fetching recent projects:', err)
        setError(err instanceof Error ? err.message : 'Failed to load projects')
        setProjects([])
      } finally {
        setIsLoading(false)
      }
    }

    fetchRecentProjects()
  }, [])

  // Fetch recent alerts on component mount and set up polling
  useEffect(() => {
    const fetchRecentAlerts = async () => {
      try {
        setAlertsLoading(true)
        setAlertsError(null)

        const response = await alertsApi.list(10) // Get 10 recent alerts

        if (response.error) {
          throw new Error(response.error)
        }

        if (response.data && response.data.alerts) {
          setAlerts(response.data.alerts)
        } else {
          setAlerts([])
        }
      } catch (err) {
        console.error('Error fetching alerts:', err)
        setAlertsError(err instanceof Error ? err.message : 'Failed to load alerts')
        setAlerts([])
      } finally {
        setAlertsLoading(false)
      }
    }

    // Initial fetch
    fetchRecentAlerts()

    // Set up polling every 2 minutes (120000 ms)
    const alertsInterval = setInterval(fetchRecentAlerts, 120000)

    // Cleanup interval on unmount
    return () => clearInterval(alertsInterval)
  }, [])

  return (
    <div className="space-y-6">
      {/* Metrics Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
        {metrics.map((metric, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">{metric.label}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metric.value}</div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Recent Projects */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Recent Projects</h3>
          <Button variant="outline" asChild>
            <Link href="/projects">View All</Link>
          </Button>
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2 text-muted-foreground">Loading projects...</span>
          </div>
        ) : error ? (
          <Card className="p-6">
            <div className="text-center text-red-600">
              <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
              <p className="font-medium">Failed to load projects</p>
              <p className="text-sm text-muted-foreground mt-1">{error}</p>
              <Button
                variant="outline"
                size="sm"
                className="mt-3"
                onClick={() => window.location.reload()}
              >
                Retry
              </Button>
            </div>
          </Card>
        ) : projects.length === 0 ? (
          <Card className="p-6">
            <div className="text-center text-muted-foreground">
              <Info className="h-8 w-8 mx-auto mb-2" />
              <p className="font-medium">No projects found</p>
              <p className="text-sm mt-1">Create your first project to get started</p>
            </div>
          </Card>
        ) : (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {projects.map((project) => (
              <Card key={project.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <CardTitle className="text-base">{project.name}</CardTitle>
                    <Badge variant={getStatusVariant(project.status)}>{project.status}</Badge>
                  </div>
                  <CardDescription>{project.description}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between text-sm text-muted-foreground">
                    <span>Datasets: {project.datasetsGenerated}</span>
                    <span>Tests: {project.testsRun}</span>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Progress</span>
                      <span>{project.progress}%</span>
                    </div>
                    <Progress value={project.progress} className="h-2" />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Risk Score:</span>
                    <span className={`font-medium ${getRiskColor(project.riskScore)}`}>{project.riskScore}</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Recent Alerts */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Recent Alerts</h3>
          <Button variant="outline" asChild>
            <Link href="/alerts">View All</Link>
          </Button>
        </div>
        {alertsLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2 text-muted-foreground">Loading alerts...</span>
          </div>
        ) : alertsError ? (
          <Card className="p-6">
            <div className="text-center text-red-600">
              <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
              <p className="font-medium">Failed to load alerts</p>
              <p className="text-sm text-muted-foreground mt-1">{alertsError}</p>
              <Button
                variant="outline"
                size="sm"
                className="mt-3"
                onClick={() => window.location.reload()}
              >
                Retry
              </Button>
            </div>
          </Card>
        ) : alerts.length === 0 ? (
          <Card className="p-6">
            <div className="text-center text-muted-foreground">
              <Info className="h-8 w-8 mx-auto mb-2" />
              <p className="font-medium">No recent alerts</p>
              <p className="text-sm mt-1">All systems are running smoothly</p>
            </div>
          </Card>
        ) : (
          <div className="space-y-3">
            {alerts.map((alert) => (
              <Card key={alert.id}>
                <CardContent className="flex items-start gap-4 p-4">
                  {getAlertIcon(alert.type)}
                  <div className="flex-1 space-y-1">
                    <div className="font-medium">{alert.title}</div>
                    <div className="text-sm text-muted-foreground">{alert.description}</div>
                    <div className="text-xs text-muted-foreground">
                      {alert.project || "System"} • {new Date(alert.timestamp).toLocaleString()}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div className="flex gap-4">
        <Button>Run Test</Button>
        <Button variant="outline">View Reports</Button>
        <Button variant="outline">Schedule Testing</Button>
      </div>
    </div>
  )
}
