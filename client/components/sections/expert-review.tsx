"use client"

import React, { use<PERSON>tate, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Textarea } from "@/components/ui/textarea"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"
import { Pa<PERSON><PERSON>, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination"
import { Search, Filter, Eye, FileText, Calendar, User, Target, BarChart3, Info, Edit, Check, X, CheckCircle, Circle } from "lucide-react"
import { datagenEventApi, datasetSamplesApi } from "@/lib/api-client"

// Available experts for real app - these are the 4 hardcoded experts
const availableExperts = [
  {
    id: "expert_003",
    name: "Dr. Sarah Chen",
    specialty: "Healthcare AI",
    rating: 4.9,
  },
  {
    id: "safety_expert",
    name: "Prof. Michael Rodriguez",
    specialty: "Safety & Security",
    rating: 4.8,
  },
  {
    id: "legal_expert",
    name: "Dr. Emily Watson",
    specialty: "Legal AI Ethics",
    rating: 4.7,
  },
  {
    id: "test_expert",
    name: "Alex Kim",
    specialty: "AI Testing",
    rating: 4.9,
  },
]

export function ExpertReview() {
  const [currentExpert, setCurrentExpert] = useState(availableExperts[0])
  const [activeTab, setActiveTab] = useState("events")
  const [selectedEvent, setSelectedEvent] = useState<string | null>(null)
  const [selectedProject, setSelectedProject] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [domainFilter, setDomainFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [events, setEvents] = useState<any[]>([])
  const [samples, setSamples] = useState<any[]>([])
  const [eventSamples, setEventSamples] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [samplesLoading, setSamplesLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [editingSample, setEditingSample] = useState<any | null>(null)
  const [editedContent, setEditedContent] = useState("")
  const [editedLabel, setEditedLabel] = useState("")
  const [cleanedSamples, setCleanedSamples] = useState<Set<string>>(new Set())

  // Pagination state
  const [eventsPage, setEventsPage] = useState(1)
  const [samplesPage, setSamplesPage] = useState(1)
  const [eventSamplesPage, setEventSamplesPage] = useState(1)
  const [totalEvents, setTotalEvents] = useState(0)
  const [totalSamples, setTotalSamples] = useState(0)
  const [totalEventSamples, setTotalEventSamples] = useState(0)
  const itemsPerPage = 20

  // Load data when component mounts or filters change
  useEffect(() => {
    setEventsPage(1) // Reset to first page when filters change
    loadEvents(1)
    setSamplesPage(1) // Reset to first page when filters change
    loadSamples(1)
  }, [currentExpert.id, domainFilter])

  // Load events when page changes
  useEffect(() => {
    loadEvents(eventsPage)
  }, [eventsPage])

  // Load samples when page changes
  useEffect(() => {
    loadSamples(samplesPage)
  }, [samplesPage])

  // Handle expert change
  const handleExpertChange = (expertId: string) => {
    const expert = availableExperts.find(e => e.id === expertId)
    if (expert) {
      setCurrentExpert(expert)
      setSelectedEvent(null)
      setSelectedProject(null)
      setEventSamples([])
      setSearchTerm("")
      setEventsPage(1)
      setSamplesPage(1)
      setEventSamplesPage(1)
    }
  }

  // Load samples for a specific event
  const loadEventSamples = async (eventId: string, page: number = 1) => {
    setSamplesLoading(true)
    try {
      const response = await datasetSamplesApi.list({
        datagen_event_id: eventId,
        limit: itemsPerPage,
        offset: (page - 1) * itemsPerPage
      })

      if (response.error) {
        console.warn("Failed to load event samples:", response.error)
        setEventSamples([])
        setTotalEventSamples(0)
      } else {
        setEventSamples(response.data?.samples || [])
        setTotalEventSamples(response.data?.total_count || 0)
      }
    } catch (err) {
      console.error("Error loading event samples:", err)
      setEventSamples([])
      setTotalEventSamples(0)
    } finally {
      setSamplesLoading(false)
    }
  }

  // Handle event selection
  const handleEventClick = (eventId: string) => {
    if (selectedEvent === eventId) {
      setSelectedEvent(null)
      setEventSamples([])
      setTotalEventSamples(0)
    } else {
      setSelectedEvent(eventId)
      setEventSamplesPage(1) // Reset to first page when selecting new event
      loadEventSamples(eventId, 1)
    }
  }

  // Load event samples when page changes
  useEffect(() => {
    if (selectedEvent) {
      loadEventSamples(selectedEvent, eventSamplesPage)
    }
  }, [eventSamplesPage])

  // Handle project click
  const handleProjectClick = (projectId: string) => {
    if (selectedProject === projectId) {
      setSelectedProject(null)
    } else {
      setSelectedProject(projectId)
      setActiveTab("samples")
    }
  }

  // Handle sample editing
  const handleEditSample = (sample: any) => {
    setEditingSample(sample)
    setEditedContent(sample.content)
    setEditedLabel(sample.label || "")
  }

  const handleSaveEdit = async () => {
    if (!editingSample) return

    try {
      // In a real app, this would call an API to update the sample
      console.log("Saving sample edit:", {
        id: editingSample.id,
        content: editedContent,
        label: editedLabel
      })

      // Update local state
      setSamples(prevSamples =>
        prevSamples.map(sample =>
          sample.id === editingSample.id
            ? { ...sample, content: editedContent, label: editedLabel }
            : sample
        )
      )

      // Close editor
      setEditingSample(null)
      setEditedContent("")
      setEditedLabel("")

      // Show success message (in a real app, you'd use a toast notification)
      alert("Sample updated successfully!")

    } catch (err) {
      console.error("Error saving sample:", err)
      alert("Failed to save sample changes")
    }
  }

  const handleCancelEdit = () => {
    setEditingSample(null)
    setEditedContent("")
    setEditedLabel("")
  }

  // Handle sample cleaning status
  const toggleSampleCleaned = (sampleId: string) => {
    setCleanedSamples(prev => {
      const newSet = new Set(prev)
      if (newSet.has(sampleId)) {
        newSet.delete(sampleId)
      } else {
        newSet.add(sampleId)
      }
      return newSet
    })
  }

  const markAllSamplesCleaned = () => {
    const allSampleIds = new Set(filteredSamples.map(s => s.id))
    setCleanedSamples(allSampleIds)
  }

  const clearAllCleanedStatus = () => {
    setCleanedSamples(new Set())
  }

  const loadEvents = async (page: number = 1) => {
    setLoading(true)
    setError(null)
    try {
      const response = await datagenEventApi.list({
        expert_id: currentExpert.id,
        domain: domainFilter !== "all" ? domainFilter : undefined,
        limit: itemsPerPage,
        offset: (page - 1) * itemsPerPage
      })

      if (response.error) {
        setError(response.error)
        setTotalEvents(0)
      } else {
        setEvents(response.data?.events || [])
        setTotalEvents(response.data?.total_count || 0)
      }
    } catch (err) {
      setError("Failed to load events")
      console.error("Error loading events:", err)
      setTotalEvents(0)
    } finally {
      setLoading(false)
    }
  }

  const loadSamples = async (page: number = 1) => {
    try {
      const response = await datasetSamplesApi.list({
        limit: itemsPerPage,
        offset: (page - 1) * itemsPerPage
      })

      if (response.error) {
        console.warn("Failed to load samples:", response.error)
        setTotalSamples(0)
      } else {
        setSamples(response.data?.samples || [])
        setTotalSamples(response.data?.total_count || 0)
      }
    } catch (err) {
      console.error("Error loading samples:", err)
      setTotalSamples(0)
    }
  }

  // Filter events based on search criteria
  const filteredEvents = events.filter(event => {
    if (searchTerm && !event.application_description?.toLowerCase().includes(searchTerm.toLowerCase()) &&
        !event.project_name?.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false
    }
    
    const mockStatus = new Date(event.created_at) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) 
      ? "pending_review" : "in_review"
    if (statusFilter !== "all" && mockStatus !== statusFilter) return false
    
    return true
  })

  // Filter samples based on selection
  const filteredSamples = selectedProject
    ? samples.filter(sample => sample.project_id === selectedProject)
    : selectedEvent
    ? eventSamples
    : samples // Show all available samples when no specific selection is made

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusBadge = (event: any) => {
    const mockStatus = new Date(event.created_at) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) 
      ? "pending_review" : "in_review"
    
    const statusConfig = {
      pending_review: { label: "Pending Review", variant: "secondary" as const },
      in_review: { label: "In Review", variant: "default" as const },
      reviewed: { label: "Reviewed", variant: "outline" as const },
      approved: { label: "Approved", variant: "default" as const }
    }
    
    const config = statusConfig[mockStatus as keyof typeof statusConfig] || statusConfig.pending_review
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Expert Review Dashboard</h1>
          <p className="text-muted-foreground mt-1">
            Review and validate dataset generation events assigned to you
          </p>
        </div>
        <div className="flex items-center gap-4">
          {/* Expert Selector */}
          <div className="flex flex-col gap-2">
            <Label className="text-xs text-muted-foreground">Switch Expert View</Label>
            <Select value={currentExpert.id} onValueChange={handleExpertChange}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {availableExperts.map((expert) => (
                  <SelectItem key={expert.id} value={expert.id}>
                    <div className="flex items-center gap-2">
                      <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center text-xs">
                        {expert.name.split(" ").map(n => n[0]).join("")}
                      </div>
                      <div>
                        <div className="font-medium">{expert.name}</div>
                        <div className="text-xs text-muted-foreground">{expert.specialty}</div>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex items-center gap-3">
            <Avatar>
              <AvatarFallback>
                {currentExpert.name.split(" ").map(n => n[0]).join("")}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="font-medium">{currentExpert.name}</div>
              <div className="text-sm text-muted-foreground">{currentExpert.specialty}</div>
              <div className="text-xs text-muted-foreground">⭐ {currentExpert.rating}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Expert Info */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <Info className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-blue-900">Expert Collaboration</h3>
              <p className="text-sm text-blue-700 mt-1">
                Switch between experts to review dataset generation events assigned to each specialist. 
                Create new events through the "Generate Dataset" section and assign experts during the process.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-blue-500" />
              <div>
                <div className="text-sm font-medium">Assigned Events</div>
                <div className="text-2xl font-bold">{totalEvents}</div>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4 text-green-500" />
              <div>
                <div className="text-sm font-medium">Dataset Samples</div>
                <div className="text-2xl font-bold">{selectedEvent ? totalEventSamples : totalSamples}</div>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Target className="h-4 w-4 text-orange-500" />
              <div>
                <div className="text-sm font-medium">Domains</div>
                <div className="text-2xl font-bold">
                  {new Set(filteredEvents.map(e => e.domain).filter(Boolean)).size}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4 text-purple-500" />
              <div>
                <div className="text-sm font-medium">Avg Complexity</div>
                <div className="text-2xl font-bold">
                  {filteredEvents.length > 0 
                    ? Math.round(filteredEvents.reduce((sum, e) => sum + (e.complexity || 0), 0) / filteredEvents.length)
                    : 0}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="events">Dataset Events</TabsTrigger>
          <TabsTrigger value="samples">Dataset Samples</TabsTrigger>
        </TabsList>

        <TabsContent value="events" className="space-y-4">
          {/* Filters */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search events by description or project name..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Filter className="h-4 w-4 text-muted-foreground" />
                  <Select value={domainFilter} onValueChange={setDomainFilter}>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="Domain" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Domains</SelectItem>
                      <SelectItem value="healthcare">Healthcare</SelectItem>
                      <SelectItem value="finance">Finance</SelectItem>
                      <SelectItem value="legal">Legal</SelectItem>
                      <SelectItem value="education">Education</SelectItem>
                      <SelectItem value="security">Security</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Events List */}
          {loading ? (
            <Card>
              <CardContent className="p-8 text-center">
                <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
                <p className="text-muted-foreground">Loading events...</p>
              </CardContent>
            </Card>
          ) : error ? (
            <Card>
              <CardContent className="p-8 text-center">
                <p className="text-red-500 mb-4">{error}</p>
                <Button onClick={loadEvents} variant="outline">
                  Try Again
                </Button>
              </CardContent>
            </Card>
          ) : filteredEvents.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">No Events Found</h3>
                <p className="text-muted-foreground mb-4">
                  No dataset generation events are assigned to {currentExpert.name} yet.
                </p>
                <p className="text-sm text-muted-foreground">
                  Create new events through the "Generate Dataset" section and assign experts during the process.
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4">
              {filteredEvents.map((event) => {
                const eventSampleCount = samples.filter(s => s.datagen_event_id === event.id).length
                const isSelected = selectedEvent === event.id
                // Show actual linked samples count, or indicate if samples need to be generated
                const displaySampleCount = eventSampleCount > 0 ? eventSampleCount : "Generate needed"

                return (
                  <Card
                    key={event.id}
                    className={`hover:shadow-md transition-all cursor-pointer ${
                      isSelected ? "ring-2 ring-primary shadow-lg" : ""
                    }`}
                    onClick={() => handleEventClick(event.id)}
                  >
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <CardTitle className="text-lg flex items-center gap-2">
                            {event.project_name || "Unnamed Project"}
                            {event.project_id && (
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 px-2 text-xs"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  handleProjectClick(event.project_id)
                                }}
                              >
                                View Project
                              </Button>
                            )}
                          </CardTitle>
                          <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <Calendar className="h-4 w-4" />
                              {formatDate(event.created_at)}
                            </div>
                            <div className="flex items-center gap-1">
                              <Target className="h-4 w-4" />
                              {event.domain || "Unknown"}
                            </div>
                            <div className="flex items-center gap-1">
                              <BarChart3 className="h-4 w-4" />
                              Complexity: {event.complexity || 0}/10
                            </div>
                            <div className="flex items-center gap-1">
                              <FileText className="h-4 w-4" />
                              {displaySampleCount} samples
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {getStatusBadge(event)}
                          <Button
                            variant={isSelected ? "default" : "outline"}
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleEventClick(event.id)
                            }}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            {isSelected ? "Hide" : "View"} Samples
                          </Button>
                        </div>
                      </div>
                    </CardHeader>

                    {selectedEvent === event.id && (
                      <CardContent className="pt-0">
                        <div className="mt-4 pt-4 border-t space-y-4">
                          <div>
                            <h4 className="font-medium mb-2">Example Input:</h4>
                            <div className="bg-muted p-3 rounded text-sm">
                              {event.example_input || "No example input provided"}
                            </div>
                          </div>

                          <div>
                            <div className="flex items-center justify-between mb-3">
                              <h4 className="font-medium">
                                Dataset Samples ({eventSamples.length} of {totalEventSamples})
                                {totalEventSamples > itemsPerPage && (
                                  <span className="text-sm text-muted-foreground ml-2">
                                    - Page {eventSamplesPage} of {Math.ceil(totalEventSamples / itemsPerPage)}
                                  </span>
                                )}
                              </h4>
                              {samplesLoading && (
                                <div className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full"></div>
                              )}
                            </div>

                            {eventSamples.length > 0 ? (
                              <div className="space-y-2 max-h-60 overflow-y-auto">
                                {eventSamples.slice(0, 5).map((sample, index) => (
                                  <div key={sample.id} className="bg-muted/50 p-3 rounded text-sm">
                                    <div className="flex items-center justify-between mb-1">
                                      <span className="font-medium text-xs text-muted-foreground">
                                        Sample {index + 1}
                                      </span>
                                      {sample.label && (
                                        <Badge variant="outline" className="text-xs">
                                          {sample.label}
                                        </Badge>
                                      )}
                                    </div>
                                    <div className="text-sm">
                                      {sample.content.length > 150
                                        ? `${sample.content.substring(0, 150)}...`
                                        : sample.content}
                                    </div>
                                  </div>
                                ))}
                                {totalEventSamples > itemsPerPage && (
                                  <div className="flex justify-center py-2">
                                    <div className="flex items-center gap-2">
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => setEventSamplesPage(Math.max(1, eventSamplesPage - 1))}
                                        disabled={eventSamplesPage === 1}
                                      >
                                        Previous
                                      </Button>
                                      <span className="text-sm text-muted-foreground">
                                        Page {eventSamplesPage} of {Math.ceil(totalEventSamples / itemsPerPage)}
                                      </span>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => setEventSamplesPage(Math.min(Math.ceil(totalEventSamples / itemsPerPage), eventSamplesPage + 1))}
                                        disabled={eventSamplesPage >= Math.ceil(totalEventSamples / itemsPerPage)}
                                      >
                                        Next
                                      </Button>
                                    </div>
                                  </div>
                                )}
                                <div className="text-center py-2">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => setActiveTab("samples")}
                                  >
                                    View all {totalEventSamples} samples in table
                                  </Button>
                                </div>
                              </div>
                            ) : (
                              <div className="text-center py-4 text-muted-foreground">
                                <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
                                <p className="text-sm">No dataset samples found for this event</p>
                                <p className="text-xs">To generate samples: Go to "Generate Dataset" → Complete the 4-step process → Samples will appear here</p>
                                <p className="text-xs mt-1">You can view all available samples in the "Dataset Samples" tab</p>
                              </div>
                            )}
                          </div>

                          <div className="flex gap-2">
                            <Button size="sm">Start Review</Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setActiveTab("samples")}
                            >
                              View All Samples
                            </Button>
                            <Button variant="outline" size="sm">Download Data</Button>
                          </div>
                        </div>
                      </CardContent>
                    )}
                  </Card>
                )
              })}
            </div>
          )}

          {/* Events Pagination */}
          {totalEvents > itemsPerPage && (
            <div className="flex justify-center mt-6">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => setEventsPage(Math.max(1, eventsPage - 1))}
                      className={eventsPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    />
                  </PaginationItem>

                  {Array.from({ length: Math.ceil(totalEvents / itemsPerPage) }, (_, i) => i + 1)
                    .filter(page => {
                      const totalPages = Math.ceil(totalEvents / itemsPerPage)
                      if (totalPages <= 7) return true
                      if (page === 1 || page === totalPages) return true
                      if (page >= eventsPage - 1 && page <= eventsPage + 1) return true
                      return false
                    })
                    .map((page, index, array) => {
                      const prevPage = array[index - 1]
                      const showEllipsis = prevPage && page - prevPage > 1

                      return (
                        <React.Fragment key={page}>
                          {showEllipsis && (
                            <PaginationItem>
                              <span className="px-3 py-2">...</span>
                            </PaginationItem>
                          )}
                          <PaginationItem>
                            <PaginationLink
                              onClick={() => setEventsPage(page)}
                              isActive={page === eventsPage}
                              className="cursor-pointer"
                            >
                              {page}
                            </PaginationLink>
                          </PaginationItem>
                        </React.Fragment>
                      )
                    })}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => setEventsPage(Math.min(Math.ceil(totalEvents / itemsPerPage), eventsPage + 1))}
                      className={eventsPage >= Math.ceil(totalEvents / itemsPerPage) ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </TabsContent>

        <TabsContent value="samples" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Dataset Samples</CardTitle>
                  <p className="text-sm text-muted-foreground">
                    {selectedEvent
                      ? `Showing ${filteredSamples.length} of ${totalEventSamples} samples for selected event (Page ${eventSamplesPage} of ${Math.ceil(totalEventSamples / itemsPerPage)})`
                      : selectedProject
                      ? `Showing samples for selected project (${filteredSamples.length} samples)`
                      : `Showing ${filteredSamples.length} of ${totalSamples} samples (Page ${samplesPage} of ${Math.ceil(totalSamples / itemsPerPage)}) - Click events to see event-specific samples`
                    }
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  {(selectedEvent || selectedProject) && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setSelectedEvent(null)
                        setSelectedProject(null)
                        setEventSamples([])
                      }}
                    >
                      Show All Samples
                    </Button>
                  )}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={markAllSamplesCleaned}
                    disabled={filteredSamples.length === 0}
                  >
                    <CheckCircle className="h-4 w-4 mr-1" />
                    Mark All Cleaned
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={clearAllCleanedStatus}
                    disabled={cleanedSamples.size === 0}
                  >
                    <Circle className="h-4 w-4 mr-1" />
                    Clear All Status
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {filteredSamples.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Samples Found</h3>
                  <p className="text-muted-foreground">
                    No dataset samples are available for the selected criteria.
                  </p>
                </div>
              ) : (
                <>
                  {/* Cleaning Progress Summary */}
                  <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-5 w-5 text-blue-600" />
                        <span className="font-medium text-blue-900">Data Cleaning Progress</span>
                      </div>
                      <div className="text-sm text-blue-700">
                        {cleanedSamples.size} of {filteredSamples.length} samples cleaned
                        ({Math.round((cleanedSamples.size / filteredSamples.length) * 100)}%)
                      </div>
                    </div>
                    <div className="mt-2 bg-blue-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${(cleanedSamples.size / filteredSamples.length) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                </>
              )}

              {filteredSamples.length > 0 && (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">Status</TableHead>
                      <TableHead>Content</TableHead>
                      <TableHead>Label</TableHead>
                      <TableHead>Event</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredSamples.map((sample) => {
                      const event = events.find(e => e.id === sample.datagen_event_id)
                      const isCleaned = cleanedSamples.has(sample.id)

                      return (
                        <TableRow key={sample.id} className={isCleaned ? "bg-green-50" : ""}>
                          <TableCell>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => toggleSampleCleaned(sample.id)}
                              className={`p-1 ${isCleaned ? "text-green-600" : "text-gray-400"}`}
                            >
                              {isCleaned ? (
                                <CheckCircle className="h-5 w-5" />
                              ) : (
                                <Circle className="h-5 w-5" />
                              )}
                            </Button>
                          </TableCell>
                          <TableCell className="max-w-md">
                            <div className="truncate" title={sample.content}>
                              {sample.content}
                            </div>
                          </TableCell>
                          <TableCell>
                            {sample.label && (
                              <Badge variant="outline">{sample.label}</Badge>
                            )}
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              <div className="font-medium">
                                {event?.project_name || "Unknown Project"}
                              </div>
                              <div className="text-muted-foreground">
                                {event?.domain || "Unknown Domain"}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="text-sm text-muted-foreground">
                            {sample.created_at ? formatDate(sample.created_at) : "Unknown"}
                          </TableCell>
                          <TableCell>
                            <div className="flex gap-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEditSample(sample)}
                                title="Edit sample"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => toggleSampleCleaned(sample.id)}
                                title={isCleaned ? "Mark as uncleaned" : "Mark as cleaned"}
                              >
                                {isCleaned ? (
                                  <CheckCircle className="h-4 w-4 text-green-600" />
                                ) : (
                                  <Circle className="h-4 w-4" />
                                )}
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>

          {/* Samples Pagination */}
          {((selectedEvent && totalEventSamples > itemsPerPage) || (!selectedEvent && totalSamples > itemsPerPage)) && (
            <div className="flex justify-center mt-6">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => {
                        if (selectedEvent) {
                          setEventSamplesPage(Math.max(1, eventSamplesPage - 1))
                        } else {
                          setSamplesPage(Math.max(1, samplesPage - 1))
                        }
                      }}
                      className={
                        (selectedEvent ? eventSamplesPage === 1 : samplesPage === 1)
                          ? "pointer-events-none opacity-50"
                          : "cursor-pointer"
                      }
                    />
                  </PaginationItem>

                  {Array.from({
                    length: Math.ceil((selectedEvent ? totalEventSamples : totalSamples) / itemsPerPage)
                  }, (_, i) => i + 1)
                    .filter(page => {
                      const totalPages = Math.ceil((selectedEvent ? totalEventSamples : totalSamples) / itemsPerPage)
                      const currentPage = selectedEvent ? eventSamplesPage : samplesPage
                      if (totalPages <= 7) return true
                      if (page === 1 || page === totalPages) return true
                      if (page >= currentPage - 1 && page <= currentPage + 1) return true
                      return false
                    })
                    .map((page, index, array) => {
                      const prevPage = array[index - 1]
                      const showEllipsis = prevPage && page - prevPage > 1
                      const currentPage = selectedEvent ? eventSamplesPage : samplesPage

                      return (
                        <React.Fragment key={page}>
                          {showEllipsis && (
                            <PaginationItem>
                              <span className="px-3 py-2">...</span>
                            </PaginationItem>
                          )}
                          <PaginationItem>
                            <PaginationLink
                              onClick={() => {
                                if (selectedEvent) {
                                  setEventSamplesPage(page)
                                } else {
                                  setSamplesPage(page)
                                }
                              }}
                              isActive={page === currentPage}
                              className="cursor-pointer"
                            >
                              {page}
                            </PaginationLink>
                          </PaginationItem>
                        </React.Fragment>
                      )
                    })}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => {
                        const totalPages = Math.ceil((selectedEvent ? totalEventSamples : totalSamples) / itemsPerPage)
                        if (selectedEvent) {
                          setEventSamplesPage(Math.min(totalPages, eventSamplesPage + 1))
                        } else {
                          setSamplesPage(Math.min(totalPages, samplesPage + 1))
                        }
                      }}
                      className={
                        (selectedEvent
                          ? eventSamplesPage >= Math.ceil(totalEventSamples / itemsPerPage)
                          : samplesPage >= Math.ceil(totalSamples / itemsPerPage)
                        ) ? "pointer-events-none opacity-50" : "cursor-pointer"
                      }
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Sample Edit Dialog */}
      <Dialog open={!!editingSample} onOpenChange={(open) => !open && handleCancelEdit()}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Dataset Sample</DialogTitle>
            <DialogDescription>
              Modify the content and label of this dataset sample. Changes will be saved to the database.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="sample-label" className="text-sm font-medium">
                Label
              </Label>
              <Input
                id="sample-label"
                value={editedLabel}
                onChange={(e) => setEditedLabel(e.target.value)}
                placeholder="Enter sample label..."
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="sample-content" className="text-sm font-medium">
                Content
              </Label>
              <Textarea
                id="sample-content"
                value={editedContent}
                onChange={(e) => setEditedContent(e.target.value)}
                placeholder="Enter sample content..."
                className="mt-1 min-h-[300px] font-mono text-sm"
                rows={15}
              />
              <div className="text-xs text-muted-foreground mt-1">
                Characters: {editedContent.length}
              </div>
            </div>

            {editingSample && (
              <div className="bg-muted p-3 rounded">
                <h4 className="text-sm font-medium mb-2">Original Sample Info:</h4>
                <div className="text-xs text-muted-foreground space-y-1">
                  <div><strong>ID:</strong> {editingSample.id}</div>
                  <div><strong>Created:</strong> {editingSample.created_at ? formatDate(editingSample.created_at) : "Unknown"}</div>
                  <div><strong>Sample Type:</strong> {editingSample.sample_type || "Unknown"}</div>
                  {editingSample.sample_group && (
                    <div><strong>Group:</strong> {editingSample.sample_group}</div>
                  )}
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={handleCancelEdit}>
              Cancel
            </Button>
            <Button
              onClick={handleSaveEdit}
              disabled={!editedContent.trim()}
            >
              <Check className="h-4 w-4 mr-1" />
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
