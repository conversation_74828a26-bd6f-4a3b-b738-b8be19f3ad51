"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Play, Square, Calendar, Database, TestTube, Loader2 } from "lucide-react"
import { targetConfigApi } from "@/lib/api-client"

const attackVectors = [
  { id: "prompt-injection", name: "Prompt Injection", description: "Test resistance to malicious prompts" },
  { id: "jailbreak", name: "Jailbreak Attempts", description: "Try to bypass safety constraints" },
  { id: "data-extraction", name: "Data Extraction", description: "Attempt to extract training data" },
  { id: "model-poisoning", name: "Model Poisoning", description: "Test input manipulation attacks" },
  { id: "adversarial", name: "Adversarial Examples", description: "Use adversarial inputs to fool model" },
  { id: "bias", name: "Bias Amplification", description: "Test for biased outputs" },
]

export function AutomateTesting() {
  const [config, setConfig] = useState({
    endpointUrl: "",
    authType: "bearer",
    apiKey: "",
    rateLimit: 60,
    model: "gpt-3.5-turbo",
    provider: "openai",
  })
  const [selectedVectors, setSelectedVectors] = useState<string[]>([])
  const [testingStatus, setTestingStatus] = useState({
    status: "Ready",
    testsRun: 0,
    successRate: "-",
  })
  const [isRunning, setIsRunning] = useState(false)
  const [results, setResults] = useState<
    Array<{
      attackType: string
      status: string
      responseTime: string
      result: string
    }>
  >([])

  // Dataset selection state
  const [availableDatasets, setAvailableDatasets] = useState<any[]>([])
  const [selectedDataset, setSelectedDataset] = useState<string>("")
  const [loadingDatasets, setLoadingDatasets] = useState(false)
  const [testingConfig, setTestingConfig] = useState(false)
  const [testResults, setTestResults] = useState<any>(null)

  // Load available datasets on component mount
  useEffect(() => {
    loadAvailableDatasets()
  }, [])

  const loadAvailableDatasets = async () => {
    setLoadingDatasets(true)
    try {
      const response = await targetConfigApi.listDatasets({ limit: 20 })
      if (response.data) {
        setAvailableDatasets(response.data.datasets || [])
      }
    } catch (error) {
      console.error("Failed to load datasets:", error)
    } finally {
      setLoadingDatasets(false)
    }
  }

  const testTargetConfiguration = async () => {
    if (!config.apiKey || !config.model) {
      alert("Please provide API key and model")
      return
    }

    setTestingConfig(true)
    setTestResults(null)

    try {
      const testData = {
        target_llm_config: {
          model: config.model,
          api_key: config.apiKey,
          base_url: config.endpointUrl || undefined,
          provider: config.provider,
          temperature: 0.7,
          max_tokens: 1024,
          top_p: 0.9,
        },
        dataset_id: selectedDataset || undefined,
        num_samples: 3,
        num_mutations: 2,
        max_iters: 1,
      }

      const response = await targetConfigApi.testRemoteModel(testData)
      if (response.data) {
        setTestResults(response.data)
      } else {
        throw new Error(response.error || "Test failed")
      }
    } catch (error) {
      console.error("Configuration test failed:", error)
      setTestResults({
        status: "failed",
        message: "Configuration test failed",
        error_details: error instanceof Error ? error.message : "Unknown error",
      })
    } finally {
      setTestingConfig(false)
    }
  }

  const toggleVector = (vectorId: string) => {
    setSelectedVectors((prev) => (prev.includes(vectorId) ? prev.filter((id) => id !== vectorId) : [...prev, vectorId]))
  }

  const startTesting = () => {
    if (!config.endpointUrl || !config.apiKey) {
      alert("Please provide endpoint URL and API key")
      return
    }
    if (selectedVectors.length === 0) {
      alert("Please select at least one attack vector")
      return
    }

    setIsRunning(true)
    setTestingStatus({ status: "Running", testsRun: 0, successRate: "-" })
    setResults([])

    // Simulate testing
    let testsRun = 0
    const interval = setInterval(() => {
      testsRun++
      const attackTypes = ["Prompt Injection", "Jailbreak", "Data Extraction", "Model Poisoning"]
      const statuses = ["Success", "Failed", "Blocked"]
      const results_list = ["Blocked", "Detected", "Bypassed"]

      const newResult = {
        attackType: attackTypes[Math.floor(Math.random() * attackTypes.length)],
        status: statuses[Math.floor(Math.random() * statuses.length)],
        responseTime: `${Math.floor(Math.random() * 500) + 100}ms`,
        result: results_list[Math.floor(Math.random() * results_list.length)],
      }

      setResults((prev) => [newResult, ...prev.slice(0, 9)])
      setTestingStatus({
        status: "Running",
        testsRun,
        successRate: `${Math.floor(Math.random() * 20) + 80}%`,
      })

      if (testsRun >= 20) {
        clearInterval(interval)
        setIsRunning(false)
        setTestingStatus((prev) => ({ ...prev, status: "Completed" }))
      }
    }, 1000)
  }

  const stopTesting = () => {
    setIsRunning(false)
    setTestingStatus((prev) => ({ ...prev, status: "Stopped" }))
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Target Configuration */}
        <Card>
          <CardHeader>
            <CardTitle>Target Configuration</CardTitle>
            <CardDescription>Configure your LLM endpoint and dataset for testing</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="model">Target Model</Label>
              <Select value={config.model} onValueChange={(value) => setConfig({ ...config, model: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select target model..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                  <SelectItem value="gpt-4">GPT-4</SelectItem>
                  <SelectItem value="gpt-4-turbo">GPT-4 Turbo</SelectItem>
                  <SelectItem value="claude-3-sonnet">Claude 3 Sonnet</SelectItem>
                  <SelectItem value="claude-3-haiku">Claude 3 Haiku</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="provider">Provider</Label>
              <Select value={config.provider} onValueChange={(value) => setConfig({ ...config, provider: value })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="openai">OpenAI</SelectItem>
                  <SelectItem value="azure_openai">Azure OpenAI</SelectItem>
                  <SelectItem value="anthropic">Anthropic</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="endpoint">Endpoint URL (Optional)</Label>
              <Input
                id="endpoint"
                type="url"
                placeholder="https://api.openai.com/v1"
                value={config.endpointUrl}
                onChange={(e) => setConfig({ ...config, endpointUrl: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="api-key">API Key</Label>
              <Input
                id="api-key"
                type="password"
                placeholder="Enter your API key"
                value={config.apiKey}
                onChange={(e) => setConfig({ ...config, apiKey: e.target.value })}
              />
            </div>

            {/* Dataset Selection */}
            <div className="space-y-2">
              <Label htmlFor="dataset">
                <Database className="inline w-4 h-4 mr-1" />
                Dataset for Custom Prompts
              </Label>
              <Select
                value={selectedDataset}
                onValueChange={setSelectedDataset}
                disabled={loadingDatasets}
              >
                <SelectTrigger>
                  <SelectValue placeholder={loadingDatasets ? "Loading datasets..." : "Select dataset..."} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Use default prompts</SelectItem>
                  {availableDatasets.map((dataset) => (
                    <SelectItem key={dataset.id} value={dataset.id}>
                      {dataset.name} ({dataset.total_samples} samples)
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {selectedDataset && (
                <p className="text-sm text-muted-foreground">
                  Selected dataset will provide custom prompts for testing
                </p>
              )}
            </div>

            {/* Test Configuration Button */}
            <Button
              onClick={testTargetConfiguration}
              disabled={testingConfig || !config.apiKey || !config.model}
              className="w-full"
            >
              {testingConfig ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Testing Configuration...
                </>
              ) : (
                <>
                  <TestTube className="mr-2 h-4 w-4" />
                  Test Configuration
                </>
              )}
            </Button>

            {/* Test Results */}
            {testResults && (
              <div className="mt-4 p-3 border rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  {testResults.status === "completed" ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-500" />
                  )}
                  <span className="font-medium">
                    {testResults.status === "completed" ? "Test Successful" : "Test Failed"}
                  </span>
                </div>
                <p className="text-sm text-muted-foreground mb-2">{testResults.message}</p>
                {testResults.status === "completed" && testResults.results && (
                  <div className="text-sm">
                    <p>Responses: {testResults.total_responses_generated}</p>
                    <p>Average Score: {testResults.average_score?.toFixed(3)}</p>
                  </div>
                )}
                {testResults.error_details && (
                  <p className="text-sm text-red-600 mt-1">{testResults.error_details}</p>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Attack Vectors */}
        <Card>
          <CardHeader>
            <CardTitle>Attack Vectors</CardTitle>
            <CardDescription>Select the types of attacks to test</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-3">
              {attackVectors.map((vector) => (
                <Card
                  key={vector.id}
                  className={`cursor-pointer transition-colors ${
                    selectedVectors.includes(vector.id) ? "ring-2 ring-primary bg-primary/5" : ""
                  }`}
                  onClick={() => toggleVector(vector.id)}
                >
                  <CardContent className="p-3">
                    <div className="font-medium text-sm">{vector.name}</div>
                    <div className="text-xs text-muted-foreground">{vector.description}</div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Test Execution */}
      <Card>
        <CardHeader>
          <CardTitle>Test Execution</CardTitle>
          <CardDescription>Monitor and control your testing session</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Controls */}
          <div className="flex gap-4">
            <Button onClick={startTesting} disabled={isRunning}>
              <Play className="mr-2 h-4 w-4" />
              Start Testing
            </Button>
            <Button variant="outline" onClick={stopTesting} disabled={!isRunning}>
              <Square className="mr-2 h-4 w-4" />
              Stop
            </Button>
            <Button variant="outline">
              <Calendar className="mr-2 h-4 w-4" />
              Schedule
            </Button>
          </div>

          {/* Status */}
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{testingStatus.status}</div>
              <div className="text-sm text-muted-foreground">Status</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{testingStatus.testsRun}</div>
              <div className="text-sm text-muted-foreground">Tests Run</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{testingStatus.successRate}</div>
              <div className="text-sm text-muted-foreground">Success Rate</div>
            </div>
          </div>

          {/* Results */}
          <div>
            <h4 className="font-medium mb-4">Live Results</h4>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Attack Type</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Response Time</TableHead>
                  <TableHead>Result</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {results.map((result, index) => (
                  <TableRow key={index}>
                    <TableCell>{result.attackType}</TableCell>
                    <TableCell>
                      <Badge variant={result.status === "Success" ? "default" : "destructive"}>{result.status}</Badge>
                    </TableCell>
                    <TableCell>{result.responseTime}</TableCell>
                    <TableCell>{result.result}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
