"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { AlertTriangle, Info, Zap } from "lucide-react"
import { alertsApi } from "@/lib/api-client"
import Link from "next/link"

interface Alert {
  id: string
  type: "critical" | "warning" | "info"
  title: string
  description: string
  project: string | null
  timestamp: string
  source: string
  details?: Record<string, any>
}

const getAlertIcon = (type: string) => {
  switch (type) {
    case "critical":
      return <AlertTriangle className="h-5 w-5 text-red-600" />
    case "warning":
      return <Zap className="h-5 w-5 text-yellow-600" />
    case "info":
      return <Info className="h-5 w-5 text-blue-600" />
    default:
      return <Info className="h-5 w-5" />
  }
}

export default function AlertsPage() {
  const [alerts, setAlerts] = useState<Alert[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [page, setPage] = useState(1)
  const [projectFilter, setProjectFilter] = useState<string>("")
  const pageSize = 20

  const fetchAlerts = async (page: number, project?: string) => {
    try {
      setLoading(true)
      setError(null)
      let urlLimit = pageSize * page
      let response
      if (project && project.trim() !== "") {
        // Fetch all alerts and filter client-side since backend does not support project filter param
        response = await alertsApi.list(urlLimit)
        if (response.data && response.data.alerts) {
          const filtered = response.data.alerts.filter((alert: Alert) =>
            alert.project && alert.project.toLowerCase().includes(project.toLowerCase())
          )
          // Sort by timestamp descending
          filtered.sort((a: Alert, b: Alert) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
          setAlerts(filtered)
        } else {
          setAlerts([])
        }
      } else {
        response = await alertsApi.list(urlLimit)
        if (response.data && response.data.alerts) {
          // Sort by timestamp descending
          const sorted = [...response.data.alerts].sort((a: Alert, b: Alert) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
          setAlerts(sorted)
        } else {
          setAlerts([])
        }
      }
      if (response.error) {
        throw new Error(response.error)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load alerts")
      setAlerts([])
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAlerts(page, projectFilter)
  }, [page, projectFilter])

  return (
    <div className="p-6">
      <div className="mb-4 flex items-center gap-4">
        <Button variant="outline" size="sm" className="ml-auto" asChild>
          <Link href="/">Back to Dashboard</Link>
        </Button>
      </div>
      <h1 className="text-2xl font-bold mb-4">All Alerts</h1>
      <div className="mb-4 flex items-center gap-4">
        <input
          type="text"
          placeholder="Filter by project"
          value={projectFilter}
          onChange={(e) => setProjectFilter(e.target.value)}
          className="border border-gray-300 rounded px-3 py-1"
        />
        <Button onClick={() => fetchAlerts(1, projectFilter)}>Apply Filter</Button>
        <Button onClick={() => { setProjectFilter(""); fetchAlerts(1); }}>Clear Filter</Button>
      </div>
      {loading ? (
        <div className="flex items-center justify-center py-8">
          <span>Loading alerts...</span>
        </div>
      ) : error ? (
        <div className="text-center text-red-600">
          <p className="font-medium">Failed to load alerts</p>
          <p>{error}</p>
          <Button onClick={() => fetchAlerts(page, projectFilter)}>Retry</Button>
        </div>
      ) : alerts.length === 0 ? (
        <div className="text-center text-muted-foreground">
          <Info className="h-8 w-8 mx-auto mb-2" />
          <p className="font-medium">No alerts found</p>
          <p>All systems are running smoothly</p>
        </div>
      ) : (
        <table className="w-full border-collapse border border-gray-300">
          <thead>
            <tr className="bg-gray-100">
              <th className="border border-gray-300 px-4 py-2 text-left">Type</th>
              <th className="border border-gray-300 px-4 py-2 text-left">Title</th>
              <th className="border border-gray-300 px-4 py-2 text-left">Description</th>
              <th className="border border-gray-300 px-4 py-2 text-left">Project</th>
              <th className="border border-gray-300 px-4 py-2 text-left">Timestamp</th>
            </tr>
          </thead>
          <tbody>
            {alerts.map((alert) => (
              <tr key={alert.id} className="hover:bg-gray-50">
                <td className="border border-gray-300 px-4 py-2">{getAlertIcon(alert.type)}</td>
                <td className="border border-gray-300 px-4 py-2">{alert.title}</td>
                <td className="border border-gray-300 px-4 py-2">{alert.description}</td>
                <td className="border border-gray-300 px-4 py-2">{alert.project || "System"}</td>
                <td className="border border-gray-300 px-4 py-2">{new Date(alert.timestamp).toLocaleString()}</td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
      {/* Pagination controls could be added here if backend supports offset */}
    </div>
  )
}